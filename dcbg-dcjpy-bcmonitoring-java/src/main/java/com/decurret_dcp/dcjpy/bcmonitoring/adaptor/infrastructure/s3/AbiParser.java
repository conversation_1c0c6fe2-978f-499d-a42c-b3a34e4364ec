package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3;

import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties;
import com.decurret_dcp.dcjpy.bcmonitoring.consts.DCFConst;
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.ContractEvents;
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.ContractInfo;
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.EventDefinition;
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.ParameterInfo;
import com.decurret_dcp.dcjpy.bcmonitoring.exception.ResourceNotFoundException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.web3j.abi.EventEncoder;
import org.web3j.abi.TypeReference;
import org.web3j.abi.datatypes.Event;
import org.web3j.protocol.core.methods.response.Log;

@Component
public class AbiParser {
  private static final Logger log = LoggerFactory.getLogger(AbiParser.class);
  private final ObjectMapper mapper = new ObjectMapper();
  public static Map<String, ContractEvents> contractEventStore = new HashMap<>();
  public static List<String> contractAddresses = new ArrayList<>();

  private final BcmonitoringConfigurationProperties properties;

  public AbiParser(BcmonitoringConfigurationProperties properties) {
    this.properties = properties;
  }

  /**
   * Parse ABI content from JSON string
   *
   * @param abiContent ABI content in JSON format
   * @throws IOException If parsing fails
   */
  public Map<String, Event> parseAbi(String abiContent) throws IOException {
    Map<String, Event> stringEventMap = new HashMap<>();
    if (abiContent == null || abiContent.isEmpty()) {
      log.warn("Empty ABI content provided");
      return stringEventMap;
    }

    try {
      JsonNode abiJson = mapper.readTree(abiContent);

      for (JsonNode node : abiJson) {
        if ("event".equals(node.get("type").asText())) {
          JsonNode nameNode = node.get("name");
          if (nameNode == null || nameNode.asText().isEmpty()) {
            // Skip events with missing or empty names
            continue;
          }

          String eventName = nameNode.asText();
          List<TypeReference<?>> parameters = new ArrayList<>();

          // Parse inputs
          JsonNode inputs = node.get("inputs");
          for (JsonNode input : inputs) {
            String type = input.get("type").asText();
            boolean indexed = input.has("indexed") && input.get("indexed").asBoolean();

            TypeReference<?> typeReference = createTypeReference(type, indexed);
            parameters.add(typeReference);
          }

          // Create event (Web3j expects 2 arguments)
          Event event = new Event(eventName, parameters);

          // Create signature for the event
          String signature = EventEncoder.encode(event);
          log.debug("Event: {}, Parameters: {}, Signature: {}", eventName, parameters, signature);

          // Store event in contractEventStore
          stringEventMap.put(signature, event);

          log.debug("Parsed event: {} with signature: {}", eventName, signature);
        }
      }

      log.info("Successfully parsed ABI with {} events", stringEventMap.size());
    } catch (Exception e) {
      log.error("Failed to parse ABI content", e);
      throw new IOException("ABI parsing failed: " + e.getMessage(), e);
    }
    return stringEventMap;
  }

  /**
   * Parse ABI content from JSON string with parameter names preserved
   *
   * @param abiContent ABI content in JSON format
   * @throws IOException If parsing fails
   */
  public Map<String, EventDefinition> parseAbiWithParameterNames(String abiContent)
      throws IOException {
    Map<String, EventDefinition> eventDefinitionMap = new HashMap<>();
    if (abiContent == null || abiContent.isEmpty()) {
      log.warn("Empty ABI content provided");
      return eventDefinitionMap;
    }

    try {
      JsonNode abiJson = mapper.readTree(abiContent);

      for (JsonNode node : abiJson) {
        if ("event".equals(node.get("type").asText())) {
          JsonNode nameNode = node.get("name");
          if (nameNode == null || nameNode.asText().isEmpty()) {
            // Skip events with missing or empty names
            continue;
          }

          String eventName = nameNode.asText();
          List<TypeReference<?>> web3jParameters = new ArrayList<>();
          List<ParameterInfo> parameterInfos = new ArrayList<>();

          // Parse inputs with parameter names
          JsonNode inputs = node.get("inputs");
          for (JsonNode input : inputs) {
            String type = input.get("type").asText();
            String paramName =
                input.has("name") ? input.get("name").asText() : "param" + parameterInfos.size();
            boolean indexed = input.has("indexed") && input.get("indexed").asBoolean();

            TypeReference<?> typeReference = createTypeReference(type, indexed);
            web3jParameters.add(typeReference);

            ParameterInfo paramInfo =
                ParameterInfo.builder()
                    .name(paramName)
                    .typeReference(typeReference)
                    .indexed(indexed)
                    .build();
            parameterInfos.add(paramInfo);
          }

          // Create Web3j event
          Event web3jEvent = new Event(eventName, web3jParameters);

          // Create enhanced event definition
          EventDefinition eventDefinition =
              EventDefinition.builder()
                  .name(eventName)
                  .web3jEvent(web3jEvent)
                  .parameters(parameterInfos)
                  .build();

          // Create signature for the event
          String signature = EventEncoder.encode(web3jEvent);
          log.debug(
              "Event: {}, Parameters: {}, Signature: {}", eventName, parameterInfos, signature);

          // Store event definition
          eventDefinitionMap.put(signature, eventDefinition);

          log.debug(
              "Parsed event with parameter names: {} with signature: {}", eventName, signature);
        }
      }

      log.info(
          "Successfully parsed ABI with {} events including parameter names",
          eventDefinitionMap.size());
    } catch (Exception e) {
      log.error("Failed to parse ABI content with parameter names", e);
      throw new IOException("ABI parsing with parameter names failed: " + e.getMessage(), e);
    }
    return eventDefinitionMap;
  }

  // todo: add convert type
  /** Create appropriate TypeReference based on the Solidity type */
  private TypeReference<?> createTypeReference(String solidityType, boolean indexed) {
    if (solidityType.startsWith(DCFConst.UINT)) {
      return new TypeReference<org.web3j.abi.datatypes.generated.Uint256>(indexed) {};
    } else if (solidityType.equals(DCFConst.ADDRESS)) {
      return new TypeReference<org.web3j.abi.datatypes.Address>(indexed) {};
    } else if (solidityType.equals(DCFConst.STRING)) {
      return new TypeReference<org.web3j.abi.datatypes.Utf8String>(indexed) {};
    } else if (solidityType.equals(DCFConst.BOOL)) {
      return new TypeReference<org.web3j.abi.datatypes.Bool>(indexed) {};
    } else if (solidityType.equals(DCFConst.BYTES_32)) {
      return new TypeReference<org.web3j.abi.datatypes.generated.Bytes32>(indexed) {};
    } else if (solidityType.startsWith(DCFConst.BYTES)) {
      return new TypeReference<org.web3j.abi.datatypes.DynamicBytes>(indexed) {};
    } else {
      // Default fallback
      return new TypeReference<>(indexed) {};
    }
  }

  /**
   * Parse ABI content from an input stream and register contracts/events
   *
   * @param inputStream The input stream containing ABI JSON
   * @param objectKey The S3 object key
   * @param lastModified Last modified timestamp
   * @return Contract information including address and name
   * @throws IOException If parsing fails
   */
  public ContractInfo parseAbiContent(InputStream inputStream, String objectKey, Date lastModified)
      throws IOException {
    try {
      byte[] abiJson = inputStream.readAllBytes();

      // Extract contract name from object key
      String[] pathParts = objectKey.split("/");
      String contractName = pathParts[1].replace(".json", "");

      // Extract address from JSON based on ABI format
      String abiFormat = properties.getAbiFormat();
      String address;

      JsonNode rootNode = mapper.readTree(abiJson);
      if ("truffle".equals(abiFormat)) {
        // For Truffle format, find address in networks section
        JsonNode networksNode = rootNode.path("networks");
        address = findFirstAddressInNetworks(networksNode);
      } else {
        // For other formats (like Hardhat), get address directly
        address = rootNode.path("address").asText();
      }

      address = address.toLowerCase();

      // Parse ABI section
      JsonNode abiNode = rootNode.path("abi");
      if (abiNode.isMissingNode()) {
        String errorMessage = "ABI section not found in JSON";
        log.error(errorMessage);
        throw new IOException(errorMessage);
      }

      // append the contract address
      appendContractAddress(address);

      // parse and register events
      parseAndRegisterEvents(address, contractName, abiNode.toString());

      log.info(
          "ABI file processed: address={}, contract_name={}, last_modified={}, events={}",
          address,
          contractName,
          lastModified,
          contractEventStore.size());

      return ContractInfo.builder()
          .address(address)
          .name(contractName)
          .lastModified(lastModified)
          .build();
    } finally {
      inputStream.close();
    }
  }

  /**
   * Find the first address in the networks section of the ABI JSON
   *
   * @param networksNode The networks node from the ABI JSON
   * @return The first address found, or an empty string if none found
   */
  private String findFirstAddressInNetworks(JsonNode networksNode) {
    if (networksNode.isObject()) {
      Iterator<JsonNode> elements = networksNode.elements();
      while (elements.hasNext()) {
        JsonNode network = elements.next();
        if (network.has(DCFConst.ADDRESS)) {
          return network.get(DCFConst.ADDRESS).asText();
        }
      }
    }
    return "";
  }

  /**
   * Adds a contract address to the list of monitored addresses
   *
   * @param address The contract address to add
   */
  public void appendContractAddress(String address) {
    if (!contractAddresses.contains(address)) {
      contractAddresses.add(address);
      log.info("Added contract address: {}", address);
    }
  }

  /**
   * Parse ABI JSON and register events for a contract
   *
   * @param address Contract address
   * @param contractName Contract name
   * @param abiJson ABI JSON string
   * @throws IOException If parsing fails
   */
  public void parseAndRegisterEvents(String address, String contractName, String abiJson)
      throws IOException {
    Map<String, Event> events = parseAbi(abiJson);
    Map<String, EventDefinition> eventDefinitions = parseAbiWithParameterNames(abiJson);

    contractEventStore.put(
        address,
        ContractEvents.builder()
            .contractName(contractName)
            .events(events)
            .eventDefinitions(eventDefinitions)
            .build());
    log.info("Registered events for contract: {} at address: {}", contractName, address);
  }

  /**
   * Retrieves the ABI event definition for a given log.
   *
   * @param log The Ethereum log to process
   * @return The ABI event definition for the log
   * @throws ResourceNotFoundException If no matching event is found
   */
  public org.web3j.abi.datatypes.Event getABIEventByLog(Log log) throws ResourceNotFoundException {
    String eventId = log.getTopics().get(0).toString().toLowerCase();
    String logAddress = log.getAddress().toString().toLowerCase();

    Map<String, org.web3j.abi.datatypes.Event> events =
        contractEventStore.containsKey(logAddress)
            ? contractEventStore.get(logAddress).events
            : Collections.emptyMap();
    if (events.containsKey(eventId)) {
      return events.get(eventId);
    }

    throw new ResourceNotFoundException(
        "Event definition not found in ABI for event ID: "
            + eventId
            + " at address: "
            + logAddress);
  }

  /**
   * Retrieves the enhanced event definition with parameter names for a given log.
   *
   * @param log The Ethereum log to process
   * @return The enhanced event definition for the log
   * @throws ResourceNotFoundException If no matching event is found
   */
  public EventDefinition getEventDefinitionByLog(Log log) throws ResourceNotFoundException {
    String eventId = log.getTopics().get(0).toString().toLowerCase();
    String logAddress = log.getAddress().toString().toLowerCase();

    Map<String, EventDefinition> eventDefinitions =
        contractEventStore.containsKey(logAddress)
            ? contractEventStore.get(logAddress).eventDefinitions
            : Collections.emptyMap();
    if (eventDefinitions.containsKey(eventId)) {
      return eventDefinitions.get(eventId);
    }

    throw new ResourceNotFoundException(
        "Event definition not found for log: " + eventId + " at address: " + logAddress);
  }
}
