package com.decurret_dcp.dcjpy.bcmonitoring.domain.model;

import java.util.List;
import lombok.Builder;
import org.web3j.abi.datatypes.Event;

/** Enhanced event definition that includes parameter names from ABI */
@Builder
public class EventDefinition {
  public final String name;
  public final Event web3jEvent;
  public final List<ParameterInfo> parameters;

  /** Get indexed parameters with their names */
  public List<ParameterInfo> getIndexedParameters() {
    return parameters.stream().filter(param -> param.indexed).toList();
  }

  /** Get non-indexed parameters with their names */
  public List<ParameterInfo> getNonIndexedParameters() {
    return parameters.stream().filter(param -> !param.indexed).toList();
  }
}
